#!/usr/bin/env python3
"""
CSV Data Classification Script

This script reads CSV files from a specified directory and classifies data as:
- EXTRACTED: Rows with text in the 'Article Title' column
- MANUAL: Rows with empty, 0, or missing 'Article Title' values

Author: Generated for data classification purposes
Date: 2025-09-26
"""

import os
import glob
import pandas as pd
import numpy as np
import warnings
import re
from datetime import datetime

# Suppress pandas warnings
warnings.filterwarnings('ignore')

def extract_pattern_from_path(directory_path):
    """
    Extract pattern like 'NWC-2026' from directory path.

    Args:
        directory_path (str): Path to the directory

    Returns:
        str: Extracted pattern or 'data' if no pattern found
    """
    # Get the directory name (last part of the path)
    dir_name = os.path.basename(directory_path.rstrip(os.sep))

    # Look for patterns like 'NWC-2026', 'ABC-123', etc. (letters-numbers)
    pattern_match = re.search(r'([A-Za-z]+[-_]?\d+)', dir_name)
    if pattern_match:
        return pattern_match.group(1)

    # Look for patterns like 'ABC123', 'NWC2026' (letters followed by numbers)
    pattern_match = re.search(r'([A-Za-z]+\d+)', dir_name)
    if pattern_match:
        return pattern_match.group(1)

    # Look for any alphanumeric pattern with separators
    pattern_match = re.search(r'([A-Za-z0-9]+[-_][A-Za-z0-9]+)', dir_name)
    if pattern_match:
        return pattern_match.group(1)

    # If no specific pattern found, use the directory name itself (cleaned)
    # Remove common words and keep meaningful parts
    cleaned_name = re.sub(r'\b(data|csv|files|input|output)\b', '', dir_name, flags=re.IGNORECASE)
    cleaned_name = re.sub(r'[^\w\-_]', '', cleaned_name).strip('_-')

    return cleaned_name if cleaned_name else 'data'

def is_empty_or_zero(value):
    """
    Check if a value is empty, zero, or should be considered as manual data.

    Args:
        value: The value to check

    Returns:
        bool: True if the value should be considered as manual (empty/zero)
    """
    if pd.isna(value):
        return True

    # Convert to string and strip whitespace
    str_value = str(value).strip()

    # Check for empty string, '0', 'nan', 'null', etc.
    if (str_value == '' or
        str_value == '0' or
        str_value.lower() in ['nan', 'null', 'none', 'na']):
        return True

    return False

def classify_csv_data(directory_path):
    """
    Read CSV files from directory and classify data based on Article Title column.
    
    Args:
        directory_path (str): Path to directory containing CSV files
        
    Returns:
        tuple: (manual_df, extracted_df, summary_stats)
    """
    
    # Change to the specified directory
    original_dir = os.getcwd()
    os.chdir(directory_path)
    
    try:
        # Find all CSV files in the directory
        csv_files = glob.glob('*.csv')
        
        if not csv_files:
            print(f"No CSV files found in directory: {directory_path}")
            return None, None, None
        
        print(f"Found {len(csv_files)} CSV files:")
        for file in csv_files:
            print(f"  - {file}")
        
        # Read and concatenate all CSV files
        print("\nReading CSV files...")
        dataframes = []
        
        for file in csv_files:
            try:
                df = pd.read_csv(file, on_bad_lines='skip', low_memory=False)
                df['source_file'] = file  # Add source file column for tracking
                dataframes.append(df)
                print(f"  ✓ Read {len(df)} rows from {file}")
            except Exception as e:
                print(f"  ✗ Error reading {file}: {str(e)}")
        
        if not dataframes:
            print("No valid CSV files could be read.")
            return None, None, None
        
        # Concatenate all dataframes
        df_combined = pd.concat(dataframes, ignore_index=True)
        print(f"\nTotal combined rows: {len(df_combined)}")
        
        # Check if 'Article Title' column exists
        if 'Article Title' not in df_combined.columns:
            print("Warning: 'Article Title' column not found in the data.")
            print("Available columns:", list(df_combined.columns))
            
            # Try to find similar column names
            similar_cols = [col for col in df_combined.columns 
                          if 'title' in col.lower() or 'article' in col.lower()]
            if similar_cols:
                print(f"Similar columns found: {similar_cols}")
                print("Please check if any of these should be used instead.")
            
            return None, None, None
        
        # Classify data based on Article Title column
        print("\nClassifying data...")
        
        # Create classification mask
        manual_mask = df_combined['Article Title'].apply(is_empty_or_zero)
        
        # Split data into manual and extracted
        manual_df = df_combined[manual_mask].copy()
        extracted_df = df_combined[~manual_mask].copy()
        
        # Add classification column
        manual_df['data_type'] = 'MANUAL'
        extracted_df['data_type'] = 'EXTRACTED'
        
        # Generate summary statistics
        summary_stats = {
            'total_rows': len(df_combined),
            'manual_rows': len(manual_df),
            'extracted_rows': len(extracted_df),
            'manual_percentage': (len(manual_df) / len(df_combined)) * 100,
            'extracted_percentage': (len(extracted_df) / len(df_combined)) * 100,
            'files_processed': len(csv_files),
            'source_files': csv_files
        }
        
        return manual_df, extracted_df, summary_stats
        
    finally:
        # Return to original directory
        os.chdir(original_dir)

def save_results(manual_df, extracted_df, summary_stats, output_dir, directory_path):
    """
    Save the classified data and summary to files.

    Args:
        manual_df (pd.DataFrame): Manual data
        extracted_df (pd.DataFrame): Extracted data
        summary_stats (dict): Summary statistics
        output_dir (str): Directory to save output files
        directory_path (str): Original directory path to extract pattern from
    """

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Extract pattern from directory path
    pattern = extract_pattern_from_path(directory_path)

    # Generate timestamp for filenames
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    # Save manual data
    if manual_df is not None and len(manual_df) > 0:
        manual_file = os.path.join(output_dir, f'{pattern}_manual_data_{timestamp}.csv')
        manual_df.to_csv(manual_file, index=False, encoding='utf-8-sig')
        print(f"Manual data saved to: {manual_file}")

    # Save extracted data
    if extracted_df is not None and len(extracted_df) > 0:
        extracted_file = os.path.join(output_dir, f'{pattern}_extracted_data_{timestamp}.csv')
        extracted_df.to_csv(extracted_file, index=False, encoding='utf-8-sig')
        print(f"Extracted data saved to: {extracted_file}")

    # Save combined data with classification
    if manual_df is not None and extracted_df is not None:
        combined_df = pd.concat([manual_df, extracted_df], ignore_index=True)
        combined_file = os.path.join(output_dir, f'{pattern}_classified_data_{timestamp}.csv')
        combined_df.to_csv(combined_file, index=False, encoding='utf-8-sig')
        print(f"Combined classified data saved to: {combined_file}")

    # Save summary report
    if summary_stats:
        summary_file = os.path.join(output_dir, f'{pattern}_classification_summary_{timestamp}.txt')
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("CSV Data Classification Summary\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"Pattern: {pattern}\n")
            f.write(f"Source Directory: {directory_path}\n")
            f.write(f"Processing Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Files Processed: {summary_stats['files_processed']}\n")
            f.write(f"Source Files: {', '.join(summary_stats['source_files'])}\n\n")
            f.write("Classification Results:\n")
            f.write(f"Total Rows: {summary_stats['total_rows']:,}\n")
            f.write(f"Manual Rows: {summary_stats['manual_rows']:,} ({summary_stats['manual_percentage']:.1f}%)\n")
            f.write(f"Extracted Rows: {summary_stats['extracted_rows']:,} ({summary_stats['extracted_percentage']:.1f}%)\n")

        print(f"Summary report saved to: {summary_file}")

def print_summary(summary_stats):
    """
    Print classification summary to console.
    
    Args:
        summary_stats (dict): Summary statistics
    """
    if not summary_stats:
        return
    
    print("\n" + "=" * 50)
    print("CLASSIFICATION SUMMARY")
    print("=" * 50)
    print(f"Files Processed: {summary_stats['files_processed']}")
    print(f"Total Rows: {summary_stats['total_rows']:,}")
    print(f"Manual Rows: {summary_stats['manual_rows']:,} ({summary_stats['manual_percentage']:.1f}%)")
    print(f"Extracted Rows: {summary_stats['extracted_rows']:,} ({summary_stats['extracted_percentage']:.1f}%)")
    print("=" * 50)

def main():
    """
    Main function to run the CSV data classification.
    """
    print("CSV Data Classification Tool")
    print("=" * 40)
    print("This script classifies data based on the 'Article Title' column:")
    print("- EXTRACTED: Rows with text in 'Article Title'")
    print("- MANUAL: Rows with empty/zero 'Article Title'")
    print()
    
    # Get directory path from user
    directory_path = input("Enter the directory path containing CSV files: ").strip()
    
    # Remove quotes if present
    if directory_path.startswith('"') and directory_path.endswith('"'):
        directory_path = directory_path[1:-1]
    elif directory_path.startswith("'") and directory_path.endswith("'"):
        directory_path = directory_path[1:-1]
    
    # Validate directory
    if not os.path.exists(directory_path):
        print(f"Error: Directory '{directory_path}' does not exist.")
        return
    
    if not os.path.isdir(directory_path):
        print(f"Error: '{directory_path}' is not a directory.")
        return
    
    # Process the CSV files
    manual_df, extracted_df, summary_stats = classify_csv_data(directory_path)
    
    if summary_stats is None:
        print("Classification failed. Please check the error messages above.")
        return
    
    # Print summary
    print_summary(summary_stats)
    
    # Ask if user wants to save results
    save_choice = input("\nDo you want to save the results? (y/n): ").strip().lower()

    if save_choice == 'y':
        output_dir = os.path.join(directory_path, 'classification_output')
        save_results(manual_df, extracted_df, summary_stats, output_dir, directory_path)
        print(f"\nResults saved to: {output_dir}")

        # Show the pattern that was extracted
        pattern = extract_pattern_from_path(directory_path)
        print(f"Pattern extracted from path: '{pattern}'")

    print("\nClassification completed!")

if __name__ == "__main__":
    main()
